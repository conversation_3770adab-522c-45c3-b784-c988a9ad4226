# Proof Tree for AMC 12B 2002 Problem 7

## Problem Statement
Find three consecutive positive integers whose product equals eight times their sum, then compute the sum of their squares.

## Root Node

### NODE_ROOT [ROOT]
**Goal**: Prove that the sum of squares of three consecutive positive integers whose product equals eight times their sum is 77.

---

## Strategy Nodes

### NODE_STRATEGY_MAIN [STRATEGY]
**Parent Node**: NODE_ROOT
**Detailed Plan**: Use algebraic approach by setting the middle integer as n, then derive and solve the resulting quadratic equation.
**Strategy**: Let the three consecutive positive integers be (n-1), n, (n+1) where n > 0. Set up equation from the given condition and solve for n.

---

## Subgoal Nodes

### NODE_SUBGOAL_SETUP [TO_EXPLORE]
**Parent Node**: NODE_STRATEGY_MAIN
**Goal**: Set up the algebraic equation from the problem conditions.
**Strategy**: Define three consecutive integers as (n-1), n, (n+1) and express their product and sum in terms of n.

### NODE_SUBGOAL_PRODUCT [TO_EXPLORE]
**Parent Node**: NODE_SUBGOAL_SETUP
**Goal**: Express the product of three consecutive integers.
**Strategy**: Calculate (n-1) × n × (n+1) = n(n²-1).

### NODE_SUBGOAL_SUM [TO_EXPLORE]
**Parent Node**: NODE_SUBGOAL_SETUP
**Goal**: Express the sum of three consecutive integers.
**Strategy**: Calculate (n-1) + n + (n+1) = 3n.

### NODE_SUBGOAL_EQUATION [TO_EXPLORE]
**Parent Node**: NODE_STRATEGY_MAIN
**Goal**: Set up and solve the main equation.
**Strategy**: From condition "product = 8 × sum", get n(n²-1) = 8 × 3n = 24n.

### NODE_SUBGOAL_SOLVE [TO_EXPLORE]
**Parent Node**: NODE_SUBGOAL_EQUATION
**Goal**: Solve the quadratic equation for n.
**Strategy**: Since n ≠ 0, divide both sides by n to get n²-1 = 24, then n² = 25, so n = 5.

### NODE_SUBGOAL_INTEGERS [TO_EXPLORE]
**Parent Node**: NODE_SUBGOAL_SOLVE
**Goal**: Find the three consecutive integers.
**Strategy**: With n = 5, the integers are 4, 5, 6.

### NODE_SUBGOAL_SQUARES [TO_EXPLORE]
**Parent Node**: NODE_SUBGOAL_INTEGERS
**Goal**: Calculate the sum of squares.
**Strategy**: Compute 4² + 5² + 6² = 16 + 25 + 36 = 77.

### NODE_SUBGOAL_VERIFY [TO_EXPLORE]
**Parent Node**: NODE_STRATEGY_MAIN
**Goal**: Verify the solution satisfies the original condition.
**Strategy**: Check that 4 × 5 × 6 = 120 and 8 × (4 + 5 + 6) = 8 × 15 = 120.

---

## Status Summary
- Total nodes: 9
- [ROOT]: 1
- [STRATEGY]: 1  
- [TO_EXPLORE]: 7
- [PROMISING]: 0
- [PROVEN]: 0
- [DEAD_END]: 0
