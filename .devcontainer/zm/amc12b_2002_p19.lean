-- AMC 12B 2002 Problem 19
-- Find the value of abc given positive reals a(b+c)=152, b(c+a)=162, c(a+b)=170

import Mathlib.Data.Real.Basic
import Mathlib.Algebra.Field.Basic
import Mathlib.Tactic.Ring
import Mathlib.Tactic.Linarith

theorem amc12b_2002_p19 (a b c : ℝ) (ha : 0 < a) (hb : 0 < b) (hc : 0 < c)
  (h1 : a * (b + c) = 152)
  (h2 : b * (c + a) = 162)
  (h3 : c * (a + b) = 170) :
  a * b * c = 720 := by
  -- Setup substitution: let x = ab, y = bc, z = ca
  let x := a * b
  let y := b * c
  let z := c * a

  -- Transform equations using substitution
  have eq1 : x + z = 152 := by
    -- a * (b + c) = a * b + a * c = x + z
    rw [← h1]
    ring
  have eq2 : x + y = 162 := by
    -- b * (c + a) = b * c + b * a = y + x
    rw [← h2]
    ring
  have eq3 : y + z = 170 := by
    -- c * (a + b) = c * a + c * b = z + y
    rw [← h3]
    ring

  -- Solve linear system
  -- From eq1: x + z = 152, eq2: x + y = 162, eq3: y + z = 170
  -- Adding all three: 2(x + y + z) = 484, so x + y + z = 242
  have sum_xyz : x + y + z = 242 := by
    have h_sum : (x + z) + (x + y) + (y + z) = 152 + 162 + 170 := by
      rw [eq1, eq2, eq3]
    have h_expand : (x + z) + (x + y) + (y + z) = 2 * x + 2 * y + 2 * z := by ring
    have h_factor : 2 * x + 2 * y + 2 * z = 2 * (x + y + z) := by ring
    rw [h_expand, h_factor] at h_sum
    have h_calc : 152 + 162 + 170 = 484 := by norm_num
    rw [h_calc] at h_sum
    linarith

  have hx : x = 72 := by
    -- x = (x + y + z) - (y + z) = 242 - 170 = 72
    have h_calc : 242 - 170 = 72 := by norm_num
    rw [← h_calc, ← sum_xyz, ← eq3]
    ring

  have hy : y = 90 := by
    -- y = (x + y + z) - (x + z) = 242 - 152 = 90
    have h_calc : 242 - 152 = 90 := by norm_num
    rw [← h_calc, ← sum_xyz, ← eq1]
    ring

  have hz : z = 80 := by
    -- z = (x + y + z) - (x + y) = 242 - 162 = 80
    have h_calc : 242 - 162 = 80 := by norm_num
    rw [← h_calc, ← sum_xyz, ← eq2]
    ring

  -- Calculate abc from ab, bc, ca
  have h_abc_sq : (a * b * c) ^ 2 = x * y * z := by
    -- (abc)² = (ab)(bc)(ca) = a²b²c² = (abc)²
    -- So we need to show (abc)² = xyz
    have h_expand : x * y * z = (a * b) * (b * c) * (c * a) := by
      simp only [x, y, z]
    rw [h_expand]
    ring

  have h_xyz : x * y * z = 518400 := by
    rw [hx, hy, hz]
    norm_num

  have h_abc : a * b * c = 720 := by
    -- Since (abc)² = xyz = 518400 and abc > 0, we have abc = √518400 = 720
    have h_pos : 0 < a * b * c := by
      apply mul_pos
      apply mul_pos
      exact ha
      exact hb
      exact hc
    have h_sq : (a * b * c) ^ 2 = 518400 := by
      rw [h_abc_sq, h_xyz]
    -- We know that 720² = 518400
    have h_720_sq : (720 : ℝ) ^ 2 = 518400 := by norm_num
    -- Since abc > 0 and (abc)² = 720², we have abc = 720
    have h_eq : (a * b * c) ^ 2 = (720 : ℝ) ^ 2 := by
      rw [h_sq, h_720_sq]
    exact Real.sqrt_sq h_pos ▸ (Real.sqrt_sq (by norm_num : (0 : ℝ) ≤ 720) ▸
      congr_arg Real.sqrt h_eq)

  exact h_abc
