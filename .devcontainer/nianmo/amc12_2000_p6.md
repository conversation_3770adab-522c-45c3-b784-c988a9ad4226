# AMC12 2000 P6 Proof Tree

## Problem Statement
Pick two distinct primes from {5, 7, 11, 13, 17}; which listed number can equal (product) – (sum)?
Options: (A) 22 (B) 60 (C) 119 (D) 180 (E) 231

## Proof Tree

### ROOT_001 [ROOT]
**Goal**: Prove that among the given options, only 119 can be expressed as pq - (p+q) where p, q are distinct primes from {5, 7, 11, 13, 17}
**Parent Node**: None
**Strategy**: Multi-step verification approach using algebraic transformation, modular arithmetic, and bounds checking

### STRATEGY_001 [STRATEGY]
**Goal**: Transform the expression pq - (p+q) into a more analyzable form
**Parent Node**: ROOT_001
**Detailed Plan**: Use algebraic manipulation to rewrite pq - (p+q) = (p-1)(q-1) - 1, which reveals structural properties about the result
**Strategy**: Algebraic transformation and factorization

### SUBGOAL_001 [TO_EXPLORE]
**Goal**: Prove the algebraic identity pq - (p+q) = (p-1)(q-1) - 1
**Parent Node**: STRATEGY_001
**Strategy**: Direct algebraic expansion and simplification

### SUBGOAL_002 [TO_EXPLORE]
**Goal**: Apply modular arithmetic test to eliminate impossible candidates
**Parent Node**: STRATEGY_001
**Strategy**: Since p, q are odd primes, (p-1)(q-1) ≡ 0 (mod 4), so pq - (p+q) ≡ 3 (mod 4)

### SUBGOAL_003 [TO_EXPLORE]
**Goal**: Establish upper bound for possible values
**Parent Node**: STRATEGY_001
**Strategy**: Use maximum primes p=13, q=17 to get upper bound (13-1)(17-1) - 1 = 191

### SUBGOAL_004 [TO_EXPLORE]
**Goal**: Verify that 119 is achievable with specific prime pair
**Parent Node**: STRATEGY_001
**Strategy**: Show that p=11, q=13 gives pq - (p+q) = 143 - 24 = 119

### SUBGOAL_005 [TO_EXPLORE]
**Goal**: Prove that other options (22, 60, 180, 231) are impossible
**Parent Node**: STRATEGY_001
**Strategy**: Apply modular arithmetic test and bound constraints to eliminate each option

### CONCLUSION_001 [TO_EXPLORE]
**Goal**: Conclude that 119 is the unique answer
**Parent Node**: ROOT_001
**Strategy**: Combine results from all subgoals to establish uniqueness
