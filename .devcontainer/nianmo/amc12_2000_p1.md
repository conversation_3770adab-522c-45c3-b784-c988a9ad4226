# AMC12 2000 P1 Proof Tree

## Problem Statement
Find three distinct positive integers whose product is 2001 and whose sum is as large as possible.

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that the maximum sum of three distinct positive integers with product 2001 is 671
**Strategy**: Use prime factorization and optimization argument to show one factor must be 1, then maximize remaining pair

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**: 
1. Find prime factorization of 2001
2. Prove one factor must be 1 for maximum sum
3. Find optimal pair of remaining factors
4. Calculate final maximum sum
**Strategy**: Prime factorization + optimization argument + exhaustive search

### SUBGOAL_001 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Find prime factorization of 2001
**Strategy**: Factor 2001 = 3 × 23 × 29
**Details**: Direct computation to establish 2001 = 3 × 23 × 29

### SUBGOAL_002 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Prove one factor must be 1 for maximum sum
**Strategy**: Contradiction argument - if all factors > 1, can increase sum by setting one factor to 1
**Details**: For optimal triple (a,b,c) with a ≥ b ≥ c ≥ 1 and abc = 2001, if c > 1, replace by (ac,b,1) to get larger sum

### SUBGOAL_003 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Find two distinct factors of 2001 with maximum sum
**Strategy**: Enumerate all factor pairs and compare sums
**Details**: Check pairs: (3,667), (23,87), (29,69) and find maximum sum

### SUBGOAL_004 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Calculate final maximum sum
**Strategy**: Add 1 to the optimal pair sum
**Details**: Sum = 1 + 3 + 667 = 671

### SUBGOAL_005 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Verify no other triple can exceed this sum
**Strategy**: Logical argument based on previous steps
**Details**: Since one factor must be 1 and (3,667) is optimal pair, 671 is maximum

## Current Status
- All subgoals are marked as [TO_EXPLORE]
- Ready to begin tactical execution phase
